/**
 * 视觉脚本物理节点
 * 提供物理系统相关的节点
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { FunctionNode } from '../nodes/FunctionNode';
import { FlowNode } from '../nodes/FlowNode';
import { EventNode } from '../nodes/EventNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { Vector3 } from '../../math/Vector3';
import { PhysicsSystem } from '../../physics/PhysicsSystem';
import { PhysicsBodyComponent } from '../../physics/components/PhysicsBodyComponent';
import { CollisionEvent, CollisionEventType } from '../../physics/collision/CollisionEvent';
import { CharacterControllerComponent } from '../../physics/components/CharacterControllerComponent';
import { CharacterController, CharacterControllerOptions } from '../../physics/character/CharacterController';
import { VehicleController, VehicleConfig } from '../../physics/vehicle/VehicleController';

/**
 * 射线检测节点
 * 执行物理射线检测
 */
export class RaycastNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入插槽
    this.addInput({
      name: 'origin',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '射线起点'
    });

    this.addInput({
      name: 'direction',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '射线方向'
    });

    this.addInput({
      name: 'maxDistance',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '最大检测距离',
      defaultValue: 100
    });

    this.addInput({
      name: 'collisionGroup',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '碰撞组',
      defaultValue: -1
    });

    this.addInput({
      name: 'collisionMask',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '碰撞掩码',
      defaultValue: -1
    });

    // 添加输出插槽
    this.addOutput({
      name: 'hit',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否命中'
    });

    this.addOutput({
      name: 'hitEntity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.OUTPUT,
      description: '命中的实体'
    });

    this.addOutput({
      name: 'hitPoint',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.OUTPUT,
      description: '命中点'
    });

    this.addOutput({
      name: 'hitNormal',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.OUTPUT,
      description: '命中法线'
    });

    this.addOutput({
      name: 'hitDistance',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '命中距离'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const origin = this.getInputValue('origin') as Vector3;
    const direction = this.getInputValue('direction') as Vector3;
    const maxDistance = this.getInputValue('maxDistance') as number;
    const collisionGroup = this.getInputValue('collisionGroup') as number;
    const collisionMask = this.getInputValue('collisionMask') as number;

    // 检查输入值是否有效
    if (!origin || !direction) {
      this.setOutputValue('hit', false);
      return false;
    }

    // 获取物理系统
    const physicsSystem = this.context.world.getSystem(PhysicsSystem);
    if (!physicsSystem) {
      this.setOutputValue('hit', false);
      return false;
    }

    // 计算射线终点
    const to = {
      x: origin.x + direction.x * maxDistance,
      y: origin.y + direction.y * maxDistance,
      z: origin.z + direction.z * maxDistance
    };

    // 执行射线检测
    const result = physicsSystem.raycastClosest(origin, to, {
      collisionFilterGroup: collisionGroup,
      collisionFilterMask: collisionMask
    });

    // 设置输出值
    this.setOutputValue('hit', result.hasHit());
    this.setOutputValue('hitEntity', result.getHitEntity());
    this.setOutputValue('hitPoint', result.getHitPoint());
    this.setOutputValue('hitNormal', result.getHitNormal());
    this.setOutputValue('hitDistance', result.getHitDistance());

    return result.hasHit();
  }
}

/**
 * 应用力节点
 * 向物理体应用力
 */
export class ApplyForceNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'force',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '力向量'
    });

    this.addInput({
      name: 'localPoint',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '局部应用点',
      defaultValue: new Vector3(0, 0, 0)
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const force = this.getInputValue('force') as Vector3;
    const localPoint = this.getInputValue('localPoint') as Vector3;

    // 检查输入值是否有效
    if (!entity || !force) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 检查实体是否有物理体组件
    if (!entity.hasComponent(PhysicsBodyComponent.type)) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 获取物理体组件
    const physicsBody = entity.getComponent(PhysicsBodyComponent.type) as PhysicsBodyComponent;

    // 应用力
    try {
      // 转换为THREE.Vector3
      const threeForce = new THREE.Vector3(force.x, force.y, force.z);
      const threeLocalPoint = localPoint ? new THREE.Vector3(localPoint.x, localPoint.y, localPoint.z) : undefined;

      physicsBody.applyForce(threeForce, threeLocalPoint);

      // 设置输出值
      this.setOutputValue('success', true);

      // 触发输出流程
      this.triggerFlow('flow');

      return true;
    } catch (error) {
      console.error('应用力失败:', error);

      // 设置输出值
      this.setOutputValue('success', false);

      // 触发输出流程
      this.triggerFlow('flow');

      return false;
    }
  }
}

/**
 * 碰撞检测节点
 * 检测两个实体之间的碰撞
 */
export class CollisionDetectionNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入插槽
    this.addInput({
      name: 'entityA',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '实体A'
    });

    this.addInput({
      name: 'entityB',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '实体B'
    });

    // 添加输出插槽
    this.addOutput({
      name: 'colliding',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否碰撞'
    });

    this.addOutput({
      name: 'contactPoint',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.OUTPUT,
      description: '接触点'
    });

    this.addOutput({
      name: 'contactNormal',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.OUTPUT,
      description: '接触法线'
    });

    this.addOutput({
      name: 'penetrationDepth',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '穿透深度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entityA = this.getInputValue('entityA') as Entity;
    const entityB = this.getInputValue('entityB') as Entity;

    // 检查输入值是否有效
    if (!entityA || !entityB) {
      this.setOutputValue('colliding', false);
      return false;
    }

    // 获取物理系统
    const physicsSystem = this.context.world.getSystem(PhysicsSystem);
    if (!physicsSystem) {
      this.setOutputValue('colliding', false);
      return false;
    }

    // 模拟碰撞检测（实际应用中应该使用物理引擎的碰撞检测）
    // 获取两个实体的物理体
    const bodyA = physicsSystem.getPhysicsBody(entityA);
    const bodyB = physicsSystem.getPhysicsBody(entityB);

    if (!bodyA || !bodyB) {
      this.setOutputValue('colliding', false);
      this.setOutputValue('contactPoint', null);
      this.setOutputValue('contactNormal', null);
      this.setOutputValue('penetrationDepth', 0);
      return false;
    }

    // 简单的距离检测（实际应用中应该使用更精确的碰撞检测）
    const posA = bodyA.getPosition();
    const posB = bodyB.getPosition();

    // 转换为THREE.Vector3进行计算
    const threeA = new THREE.Vector3(posA.x, posA.y, posA.z);
    const threeB = new THREE.Vector3(posB.x, posB.y, posB.z);
    const distance = threeA.distanceTo(threeB);
    const colliding = distance < 2.0; // 简单的阈值检测

    // 设置输出值
    this.setOutputValue('colliding', colliding);
    if (colliding) {
      // 计算接触点和法线
      const contactPoint = threeA.clone().lerp(threeB, 0.5);
      const contactNormal = threeB.clone().sub(threeA).normalize();
      this.setOutputValue('contactPoint', contactPoint);
      this.setOutputValue('contactNormal', contactNormal);
      this.setOutputValue('penetrationDepth', 2.0 - distance);
    } else {
      this.setOutputValue('contactPoint', null);
      this.setOutputValue('contactNormal', null);
      this.setOutputValue('penetrationDepth', 0);
    }

    return colliding;
  }
}

/**
 * 物理约束节点
 * 创建物理约束
 */
export class CreateConstraintNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entityA',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '实体A'
    });

    this.addInput({
      name: 'entityB',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '实体B'
    });

    this.addInput({
      name: 'constraintType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '约束类型',
      defaultValue: 'hinge'
    });

    this.addInput({
      name: 'pivotA',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '实体A上的枢轴点',
      defaultValue: new Vector3(0, 0, 0)
    });

    this.addInput({
      name: 'pivotB',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '实体B上的枢轴点',
      defaultValue: new Vector3(0, 0, 0)
    });

    this.addInput({
      name: 'axisA',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '实体A上的轴',
      defaultValue: new Vector3(0, 1, 0)
    });

    this.addInput({
      name: 'axisB',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '实体B上的轴',
      defaultValue: new Vector3(0, 1, 0)
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    this.addOutput({
      name: 'constraintId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '约束ID'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entityA = this.getInputValue('entityA') as Entity;
    const entityB = this.getInputValue('entityB') as Entity;
    const constraintType = this.getInputValue('constraintType') as string;
    const pivotA = this.getInputValue('pivotA') as Vector3;
    const pivotB = this.getInputValue('pivotB') as Vector3;
    const axisA = this.getInputValue('axisA') as Vector3;
    const axisB = this.getInputValue('axisB') as Vector3;

    // 检查输入值是否有效
    if (!entityA || !entityB) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 获取物理系统
    const physicsSystem = this.context.world.getSystem(PhysicsSystem);
    if (!physicsSystem) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 模拟约束创建（实际应用中应该使用物理引擎的约束系统）
    try {
      // 获取两个实体的物理体
      const bodyA = physicsSystem.getPhysicsBody(entityA);
      const bodyB = physicsSystem.getPhysicsBody(entityB);

      if (!bodyA || !bodyB) {
        this.setOutputValue('constraintId', '');
        this.setOutputValue('success', false);
        this.triggerFlow('flow');
        return false;
      }

      // 生成约束ID
      const constraintId = `constraint_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // 这里应该创建实际的物理约束，但由于PhysicsSystem没有相应方法，
      // 我们只是模拟成功创建
      console.log(`创建${constraintType}约束: ${constraintId}`, {
        entityA: entityA.id,
        entityB: entityB.id,
        pivotA,
        pivotB,
        axisA,
        axisB
      });

      // 设置输出值
      this.setOutputValue('constraintId', constraintId);
      this.setOutputValue('success', true);

      // 触发输出流程
      this.triggerFlow('flow');

      return true;
    } catch (error) {
      console.error('创建约束失败:', error);

      this.setOutputValue('constraintId', '');
      this.setOutputValue('success', false);
      this.triggerFlow('flow');

      return false;
    }
  }
}

/**
 * 物理材质节点
 * 创建物理材质
 */
export class CreatePhysicsMaterialNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入插槽
    this.addInput({
      name: 'friction',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '摩擦系数',
      defaultValue: 0.3
    });

    this.addInput({
      name: 'restitution',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '恢复系数',
      defaultValue: 0.3
    });

    // 添加输出插槽
    this.addOutput({
      name: 'material',
      type: SocketType.DATA,
      dataType: 'PhysicsMaterial',
      direction: SocketDirection.OUTPUT,
      description: '物理材质'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const friction = this.getInputValue('friction') as number;
    const restitution = this.getInputValue('restitution') as number;

    // 获取物理系统
    const physicsSystem = this.context.world.getSystem(PhysicsSystem);
    if (!physicsSystem) {
      return null;
    }

    // 创建物理材质（使用CANNON.js的Material）
    try {
      // 直接使用CANNON.js创建材质
      const material = new (physicsSystem.getPhysicsWorld().constructor as any).Material({
        friction,
        restitution
      });

      // 设置输出值
      this.setOutputValue('material', material);

      return material;
    } catch (error) {
      console.error('创建物理材质失败:', error);

      // 创建一个简单的材质对象作为备用
      const fallbackMaterial = {
        friction,
        restitution,
        type: 'PhysicsMaterial'
      };

      this.setOutputValue('material', fallbackMaterial);
      return fallbackMaterial;
    }
  }
}

/**
 * 碰撞结束事件节点 (151)
 * 检测碰撞结束事件
 */
export class OnCollisionExitNode extends EventNode {
  /** 监听的物理体A */
  private physicsBodyA: PhysicsBodyComponent | null = null;
  /** 监听的物理体B */
  private physicsBodyB: PhysicsBodyComponent | null = null;
  /** 物理系统引用 */
  private physicsSystem: PhysicsSystem | null = null;

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入插槽
    this.addInput({
      name: 'physicsBodyA',
      type: SocketType.DATA,
      dataType: 'PhysicsBodyComponent',
      direction: SocketDirection.INPUT,
      description: '物理体A'
    });

    this.addInput({
      name: 'physicsBodyB',
      type: SocketType.DATA,
      dataType: 'PhysicsBodyComponent',
      direction: SocketDirection.INPUT,
      description: '物理体B（可选）',
      defaultValue: null
    });

    // 添加输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '碰撞结束时触发'
    });

    this.addOutput({
      name: 'entityA',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.OUTPUT,
      description: '碰撞实体A'
    });

    this.addOutput({
      name: 'entityB',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.OUTPUT,
      description: '碰撞实体B'
    });

    this.addOutput({
      name: 'contactPoint',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.OUTPUT,
      description: '碰撞点'
    });

    this.addOutput({
      name: 'contactNormal',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.OUTPUT,
      description: '碰撞法线'
    });
  }

  /**
   * 初始化节点
   */
  public initialize(): void {
    super.initialize();

    // 获取物理系统
    this.physicsSystem = this.context.world.getSystem(PhysicsSystem);
    if (!this.physicsSystem) {
      console.warn('OnCollisionExitNode: 未找到物理系统');
      return;
    }

    // 监听碰撞结束事件
    this.physicsSystem.on(CollisionEventType.END, this.handleCollisionEnd.bind(this));
  }

  /**
   * 处理碰撞结束事件
   * @param event 碰撞事件
   */
  private handleCollisionEnd(event: CollisionEvent): void {
    // 获取输入的物理体
    this.physicsBodyA = this.getInputValue('physicsBodyA') as PhysicsBodyComponent;
    this.physicsBodyB = this.getInputValue('physicsBodyB') as PhysicsBodyComponent;

    // 检查是否匹配监听的物理体
    if (!this.isMatchingCollision(event)) {
      return;
    }

    // 设置输出值
    this.setOutputValue('entityA', event.entityA);
    this.setOutputValue('entityB', event.entityB);
    this.setOutputValue('contactPoint', event.contactPoint);
    this.setOutputValue('contactNormal', event.contactNormal);

    // 触发流程
    this.triggerFlow('flow');
  }

  /**
   * 检查碰撞是否匹配监听条件
   * @param event 碰撞事件
   * @returns 是否匹配
   */
  private isMatchingCollision(event: CollisionEvent): boolean {
    if (!this.physicsBodyA) {
      return false;
    }

    // 获取实体的物理体组件
    const bodyA = event.entityA.getComponent(PhysicsBodyComponent.type) as PhysicsBodyComponent;
    const bodyB = event.entityB.getComponent(PhysicsBodyComponent.type) as PhysicsBodyComponent;

    // 检查是否匹配物理体A
    const matchesA = (bodyA === this.physicsBodyA) || (bodyB === this.physicsBodyA);

    // 如果指定了物理体B，也需要匹配
    if (this.physicsBodyB) {
      const matchesB = (bodyA === this.physicsBodyB) || (bodyB === this.physicsBodyB);
      return matchesA && matchesB;
    }

    return matchesA;
  }

  /**
   * 销毁节点
   */
  public dispose(): void {
    // 移除事件监听
    if (this.physicsSystem) {
      this.physicsSystem.off(CollisionEventType.END, this.handleCollisionEnd.bind(this));
    }

    super.dispose();
  }
}

/**
 * 触发器进入事件节点 (152)
 * 检测触发器进入事件
 */
export class OnTriggerEnterNode extends EventNode {
  /** 监听的触发器 */
  private triggerBody: PhysicsBodyComponent | null = null;
  /** 目标物体 */
  private targetBody: PhysicsBodyComponent | null = null;
  /** 物理系统引用 */
  private physicsSystem: PhysicsSystem | null = null;

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入插槽
    this.addInput({
      name: 'trigger',
      type: SocketType.DATA,
      dataType: 'PhysicsBodyComponent',
      direction: SocketDirection.INPUT,
      description: '触发器物理体'
    });

    this.addInput({
      name: 'targetBody',
      type: SocketType.DATA,
      dataType: 'PhysicsBodyComponent',
      direction: SocketDirection.INPUT,
      description: '目标物体（可选）',
      defaultValue: null
    });

    // 添加输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '触发器进入时触发'
    });

    this.addOutput({
      name: 'triggerEntity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.OUTPUT,
      description: '触发器实体'
    });

    this.addOutput({
      name: 'enteringEntity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.OUTPUT,
      description: '进入的实体'
    });
  }

  /**
   * 初始化节点
   */
  public initialize(): void {
    super.initialize();

    // 获取物理系统
    this.physicsSystem = this.context.world.getSystem(PhysicsSystem);
    if (!this.physicsSystem) {
      console.warn('OnTriggerEnterNode: 未找到物理系统');
      return;
    }

    // 监听触发器进入事件
    this.physicsSystem.on(CollisionEventType.TRIGGER_ENTER, this.handleTriggerEnter.bind(this));
  }

  /**
   * 处理触发器进入事件
   * @param event 碰撞事件
   */
  private handleTriggerEnter(event: CollisionEvent): void {
    // 获取输入的触发器和目标物体
    this.triggerBody = this.getInputValue('trigger') as PhysicsBodyComponent;
    this.targetBody = this.getInputValue('targetBody') as PhysicsBodyComponent;

    // 检查是否匹配监听的触发器
    if (!this.isMatchingTrigger(event)) {
      return;
    }

    // 确定哪个是触发器，哪个是进入的实体
    const triggerBodyA = event.entityA.getComponent(PhysicsBodyComponent.type) as PhysicsBodyComponent;
    const triggerBodyB = event.entityB.getComponent(PhysicsBodyComponent.type) as PhysicsBodyComponent;

    let triggerEntity = event.entityA;
    let enteringEntity = event.entityB;

    // 如果实体A不是触发器，则交换
    if (triggerBodyA !== this.triggerBody) {
      triggerEntity = event.entityB;
      enteringEntity = event.entityA;
    }

    // 设置输出值
    this.setOutputValue('triggerEntity', triggerEntity);
    this.setOutputValue('enteringEntity', enteringEntity);

    // 触发流程
    this.triggerFlow('flow');
  }

  /**
   * 检查触发器事件是否匹配监听条件
   * @param event 碰撞事件
   * @returns 是否匹配
   */
  private isMatchingTrigger(event: CollisionEvent): boolean {
    if (!this.triggerBody) {
      return false;
    }

    // 获取实体的物理体组件
    const bodyA = event.entityA.getComponent(PhysicsBodyComponent.type) as PhysicsBodyComponent;
    const bodyB = event.entityB.getComponent(PhysicsBodyComponent.type) as PhysicsBodyComponent;

    // 检查是否包含指定的触发器
    const hasTrigger = (bodyA === this.triggerBody) || (bodyB === this.triggerBody);

    if (!hasTrigger) {
      return false;
    }

    // 如果指定了目标物体，检查是否匹配
    if (this.targetBody) {
      const hasTarget = (bodyA === this.targetBody) || (bodyB === this.targetBody);
      return hasTarget;
    }

    return true;
  }

  /**
   * 销毁节点
   */
  public dispose(): void {
    // 移除事件监听
    if (this.physicsSystem) {
      this.physicsSystem.off(CollisionEventType.TRIGGER_ENTER, this.handleTriggerEnter.bind(this));
    }

    super.dispose();
  }
}

/**
 * 触发器退出事件节点 (153)
 * 检测触发器退出事件
 */
export class OnTriggerExitNode extends EventNode {
  /** 监听的触发器 */
  private triggerBodyExit: PhysicsBodyComponent | null = null;
  /** 目标物体 */
  private targetBody: PhysicsBodyComponent | null = null;
  /** 物理系统引用 */
  private physicsSystem: PhysicsSystem | null = null;

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入插槽
    this.addInput({
      name: 'trigger',
      type: SocketType.DATA,
      dataType: 'PhysicsBodyComponent',
      direction: SocketDirection.INPUT,
      description: '触发器物理体'
    });

    this.addInput({
      name: 'targetBody',
      type: SocketType.DATA,
      dataType: 'PhysicsBodyComponent',
      direction: SocketDirection.INPUT,
      description: '目标物体（可选）',
      defaultValue: null
    });

    // 添加输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '触发器退出时触发'
    });

    this.addOutput({
      name: 'triggerEntity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.OUTPUT,
      description: '触发器实体'
    });

    this.addOutput({
      name: 'exitingEntity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.OUTPUT,
      description: '退出的实体'
    });
  }

  /**
   * 初始化节点
   */
  public initialize(): void {
    super.initialize();

    // 获取物理系统
    this.physicsSystem = this.context.world.getSystem(PhysicsSystem);
    if (!this.physicsSystem) {
      console.warn('OnTriggerExitNode: 未找到物理系统');
      return;
    }

    // 监听触发器退出事件
    this.physicsSystem.on(CollisionEventType.TRIGGER_EXIT, this.handleTriggerExit.bind(this));
  }

  /**
   * 处理触发器退出事件
   * @param event 碰撞事件
   */
  private handleTriggerExit(event: CollisionEvent): void {
    // 获取输入的触发器和目标物体
    this.triggerBodyExit = this.getInputValue('trigger') as PhysicsBodyComponent;
    this.targetBody = this.getInputValue('targetBody') as PhysicsBodyComponent;

    // 检查是否匹配监听的触发器
    if (!this.isMatchingTrigger(event)) {
      return;
    }

    // 确定哪个是触发器，哪个是退出的实体
    const triggerBodyA = event.entityA.getComponent(PhysicsBodyComponent.type) as PhysicsBodyComponent;
    const triggerBodyB = event.entityB.getComponent(PhysicsBodyComponent.type) as PhysicsBodyComponent;

    let triggerEntity = event.entityA;
    let exitingEntity = event.entityB;

    // 如果实体A不是触发器，则交换
    if (triggerBodyA !== this.triggerBodyExit) {
      triggerEntity = event.entityB;
      exitingEntity = event.entityA;
    }

    // 设置输出值
    this.setOutputValue('triggerEntity', triggerEntity);
    this.setOutputValue('exitingEntity', exitingEntity);

    // 触发流程
    this.triggerFlow('flow');
  }

  /**
   * 检查触发器事件是否匹配监听条件
   * @param event 碰撞事件
   * @returns 是否匹配
   */
  private isMatchingTrigger(event: CollisionEvent): boolean {
    if (!this.triggerBodyExit) {
      return false;
    }

    // 获取实体的物理体组件
    const bodyA = event.entityA.getComponent(PhysicsBodyComponent.type) as PhysicsBodyComponent;
    const bodyB = event.entityB.getComponent(PhysicsBodyComponent.type) as PhysicsBodyComponent;

    // 检查是否包含指定的触发器
    const hasTrigger = (bodyA === this.triggerBodyExit) || (bodyB === this.triggerBodyExit);

    if (!hasTrigger) {
      return false;
    }

    // 如果指定了目标物体，检查是否匹配
    if (this.targetBody) {
      const hasTarget = (bodyA === this.targetBody) || (bodyB === this.targetBody);
      return hasTarget;
    }

    return true;
  }

  /**
   * 销毁节点
   */
  public dispose(): void {
    // 移除事件监听
    if (this.physicsSystem) {
      this.physicsSystem.off(CollisionEventType.TRIGGER_EXIT, this.handleTriggerExit.bind(this));
    }

    super.dispose();
  }
}

/**
 * 设置重力节点 (154)
 * 设置物理世界重力
 */
export class SetGravityNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加重力向量输入
    this.addInput({
      name: 'gravity',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '重力向量',
      defaultValue: new Vector3(0, -9.82, 0)
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });
  }

  /**
   * 执行节点
   */
  public execute(): void {
    // 获取重力向量
    const gravity = this.getInputValue('gravity') as Vector3;

    if (!gravity) {
      console.warn('SetGravityNode: 重力向量无效');
      this.triggerFlow('flow');
      return;
    }

    // 获取物理系统
    const physicsSystem = this.context.world.getSystem(PhysicsSystem);
    if (!physicsSystem) {
      console.warn('SetGravityNode: 未找到物理系统');
      this.triggerFlow('flow');
      return;
    }

    // 设置重力
    physicsSystem.setGravity(gravity);

    // 触发输出流程
    this.triggerFlow('flow');
  }
}

/**
 * 设置时间步长节点 (155)
 * 设置物理模拟精度
 */
export class SetTimeStepNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加时间步长输入
    this.addInput({
      name: 'timeStep',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '时间步长（秒）',
      defaultValue: 1/60
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });
  }

  /**
   * 执行节点
   */
  public execute(): void {
    // 获取时间步长
    const timeStep = this.getInputValue('timeStep') as number;

    if (typeof timeStep !== 'number' || timeStep <= 0) {
      console.warn('SetTimeStepNode: 时间步长无效');
      this.triggerFlow('flow');
      return;
    }

    // 获取物理系统
    const physicsSystem = this.context.world.getSystem(PhysicsSystem);
    if (!physicsSystem) {
      console.warn('SetTimeStepNode: 未找到物理系统');
      this.triggerFlow('flow');
      return;
    }

    // 设置时间步长
    physicsSystem.setTimeStep(timeStep);

    // 触发输出流程
    this.triggerFlow('flow');
  }
}

/**
 * 创建角色控制器节点 (156)
 * 创建角色物理控制器
 */
export class CreateCharacterControllerNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    // 添加配置输入
    this.addInput({
      name: 'offset',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '偏移量',
      defaultValue: 0.01
    });

    this.addInput({
      name: 'maxSlopeClimbAngle',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '最大爬坡角度（度）',
      defaultValue: 45
    });

    this.addInput({
      name: 'maxStepHeight',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '最大台阶高度',
      defaultValue: 0.5
    });

    this.addInput({
      name: 'snapToGroundDistance',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '地面吸附距离',
      defaultValue: 0.1
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加角色控制器输出
    this.addOutput({
      name: 'characterController',
      type: SocketType.DATA,
      dataType: 'CharacterControllerComponent',
      direction: SocketDirection.OUTPUT,
      description: '角色控制器组件'
    });
  }

  /**
   * 执行节点
   */
  public execute(): void {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const offset = this.getInputValue('offset') as number;
    const maxSlopeClimbAngle = this.getInputValue('maxSlopeClimbAngle') as number;
    const maxStepHeight = this.getInputValue('maxStepHeight') as number;
    const snapToGroundDistance = this.getInputValue('snapToGroundDistance') as number;

    if (!entity) {
      console.warn('CreateCharacterControllerNode: 实体无效');
      this.triggerFlow('flow');
      return;
    }

    // 创建角色控制器配置
    const options: CharacterControllerOptions = {
      offset,
      maxSlopeClimbAngle: (maxSlopeClimbAngle * Math.PI) / 180, // 转换为弧度
      minSlopeSlideAngle: Math.PI / 6, // 30度
      autoStep: {
        maxHeight: maxStepHeight,
        minWidth: 0.1,
        stepOverDynamic: true
      },
      enableSnapToGround: snapToGroundDistance > 0 ? snapToGroundDistance : false
    };

    // 创建角色控制器组件
    const characterController = new CharacterControllerComponent(options);

    // 添加到实体
    entity.addComponent(characterController);

    // 设置输出值
    this.setOutputValue('characterController', characterController);

    // 触发输出流程
    this.triggerFlow('flow');
  }
}

/**
 * 移动角色节点 (157)
 * 控制角色移动
 */
export class MoveCharacterNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加角色控制器输入
    this.addInput({
      name: 'characterController',
      type: SocketType.DATA,
      dataType: 'CharacterControllerComponent',
      direction: SocketDirection.INPUT,
      description: '角色控制器'
    });

    // 添加移动向量输入
    this.addInput({
      name: 'movement',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '移动向量'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加实际移动距离输出
    this.addOutput({
      name: 'actualMovement',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.OUTPUT,
      description: '实际移动距离'
    });

    // 添加是否在地面输出
    this.addOutput({
      name: 'isGrounded',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否在地面'
    });
  }

  /**
   * 执行节点
   */
  public execute(): void {
    // 获取输入值
    const characterController = this.getInputValue('characterController') as CharacterControllerComponent;
    const movement = this.getInputValue('movement') as Vector3;

    if (!characterController || !movement) {
      console.warn('MoveCharacterNode: 角色控制器或移动向量无效');
      this.setOutputValue('actualMovement', new Vector3(0, 0, 0));
      this.setOutputValue('isGrounded', false);
      this.triggerFlow('flow');
      return;
    }

    // 转换为THREE.Vector3
    const threeMovement = new THREE.Vector3(movement.x, movement.y, movement.z);

    // 执行移动
    characterController.computeColliderMovement(threeMovement);

    // 获取实际移动距离
    const actualMovement = characterController.getComputedMovement();

    // 设置输出值
    this.setOutputValue('actualMovement', new Vector3(actualMovement.x, actualMovement.y, actualMovement.z));
    this.setOutputValue('isGrounded', characterController.isGrounded());

    // 触发输出流程
    this.triggerFlow('flow');
  }
}

/**
 * 角色跳跃节点 (158)
 * 控制角色跳跃
 */
export class JumpCharacterNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加角色控制器输入
    this.addInput({
      name: 'characterController',
      type: SocketType.DATA,
      dataType: 'CharacterControllerComponent',
      direction: SocketDirection.INPUT,
      description: '角色控制器'
    });

    // 添加跳跃力度输入
    this.addInput({
      name: 'jumpForce',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '跳跃力度',
      defaultValue: 5.0
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加是否成功跳跃输出
    this.addOutput({
      name: 'jumpSuccessful',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功跳跃'
    });
  }

  /**
   * 执行节点
   */
  public execute(): void {
    // 获取输入值
    const characterController = this.getInputValue('characterController') as CharacterControllerComponent;
    const jumpForce = this.getInputValue('jumpForce') as number;

    if (!characterController) {
      console.warn('JumpCharacterNode: 角色控制器无效');
      this.setOutputValue('jumpSuccessful', false);
      this.triggerFlow('flow');
      return;
    }

    // 检查是否在地面上
    const isGrounded = characterController.isGrounded();

    if (!isGrounded) {
      // 不在地面上，无法跳跃
      this.setOutputValue('jumpSuccessful', false);
      this.triggerFlow('flow');
      return;
    }

    // 获取实体的物理体组件
    const entity = characterController.getEntity();
    const physicsBody = entity.getComponent(PhysicsBodyComponent.type) as PhysicsBodyComponent;

    if (!physicsBody) {
      console.warn('JumpCharacterNode: 实体没有物理体组件');
      this.setOutputValue('jumpSuccessful', false);
      this.triggerFlow('flow');
      return;
    }

    // 应用跳跃冲量
    const jumpImpulse = new THREE.Vector3(0, jumpForce, 0);
    physicsBody.applyImpulse(jumpImpulse);

    // 设置输出值
    this.setOutputValue('jumpSuccessful', true);

    // 触发输出流程
    this.triggerFlow('flow');
  }
}

/**
 * 创建载具节点 (159)
 * 创建物理载具
 */
export class CreateVehicleNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '载具实体'
    });

    // 添加载具配置输入
    this.addInput({
      name: 'maxEngineForce',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '最大引擎力',
      defaultValue: 1000
    });

    this.addInput({
      name: 'maxBrakeForce',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '最大制动力',
      defaultValue: 100
    });

    this.addInput({
      name: 'maxSteeringAngle',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '最大转向角度（度）',
      defaultValue: 30
    });

    this.addInput({
      name: 'chassisMass',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '底盘质量',
      defaultValue: 800
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加载具控制器输出
    this.addOutput({
      name: 'vehicleController',
      type: SocketType.DATA,
      dataType: 'VehicleController',
      direction: SocketDirection.OUTPUT,
      description: '载具控制器'
    });
  }

  /**
   * 执行节点
   */
  public execute(): void {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const maxEngineForce = this.getInputValue('maxEngineForce') as number;
    const maxBrakeForce = this.getInputValue('maxBrakeForce') as number;
    const maxSteeringAngle = this.getInputValue('maxSteeringAngle') as number;
    const chassisMass = this.getInputValue('chassisMass') as number;

    if (!entity) {
      console.warn('CreateVehicleNode: 实体无效');
      this.triggerFlow('flow');
      return;
    }

    // 创建载具配置
    const config: VehicleConfig = {
      maxEngineForce,
      maxBrakeForce,
      maxSteeringAngle: (maxSteeringAngle * Math.PI) / 180, // 转换为弧度
      chassisMass
    };

    // 创建载具控制器
    const vehicleController = new VehicleController(entity, config);

    // 初始化载具控制器
    vehicleController.initialize();

    // 添加到实体
    entity.addComponent(vehicleController);

    // 设置输出值
    this.setOutputValue('vehicleController', vehicleController);

    // 触发输出流程
    this.triggerFlow('flow');
  }
}

/**
 * 设置引擎力节点 (160)
 * 设置载具引擎推力
 */
export class SetEngineForceNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加载具控制器输入
    this.addInput({
      name: 'vehicleController',
      type: SocketType.DATA,
      dataType: 'VehicleController',
      direction: SocketDirection.INPUT,
      description: '载具控制器'
    });

    // 添加引擎力输入
    this.addInput({
      name: 'engineForce',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '引擎力（-1到1）',
      defaultValue: 0
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });
  }

  /**
   * 执行节点
   */
  public execute(): void {
    // 获取输入值
    const vehicleController = this.getInputValue('vehicleController') as VehicleController;
    const engineForce = this.getInputValue('engineForce') as number;

    if (!vehicleController) {
      console.warn('SetEngineForceNode: 载具控制器无效');
      this.triggerFlow('flow');
      return;
    }

    // 设置引擎力
    vehicleController.setEngineForce(engineForce);

    // 触发输出流程
    this.triggerFlow('flow');
  }
}

/**
 * 设置制动力节点 (161)
 * 设置载具制动力
 */
export class SetBrakeForceNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加载具控制器输入
    this.addInput({
      name: 'vehicleController',
      type: SocketType.DATA,
      dataType: 'VehicleController',
      direction: SocketDirection.INPUT,
      description: '载具控制器'
    });

    // 添加制动力输入
    this.addInput({
      name: 'brakeForce',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '制动力（0到1）',
      defaultValue: 0
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });
  }

  /**
   * 执行节点
   */
  public execute(): void {
    // 获取输入值
    const vehicleController = this.getInputValue('vehicleController') as VehicleController;
    const brakeForce = this.getInputValue('brakeForce') as number;

    if (!vehicleController) {
      console.warn('SetBrakeForceNode: 载具控制器无效');
      this.triggerFlow('flow');
      return;
    }

    // 设置制动力
    vehicleController.setBrakeForce(brakeForce);

    // 触发输出流程
    this.triggerFlow('flow');
  }
}

/**
 * 设置转向值节点 (162)
 * 设置载具转向角度
 */
export class SetSteeringValueNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加载具控制器输入
    this.addInput({
      name: 'vehicleController',
      type: SocketType.DATA,
      dataType: 'VehicleController',
      direction: SocketDirection.INPUT,
      description: '载具控制器'
    });

    // 添加转向值输入
    this.addInput({
      name: 'steeringValue',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '转向值（-1到1）',
      defaultValue: 0
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });
  }

  /**
   * 执行节点
   */
  public execute(): void {
    // 获取输入值
    const vehicleController = this.getInputValue('vehicleController') as VehicleController;
    const steeringValue = this.getInputValue('steeringValue') as number;

    if (!vehicleController) {
      console.warn('SetSteeringValueNode: 载具控制器无效');
      this.triggerFlow('flow');
      return;
    }

    // 设置转向值
    vehicleController.setSteeringValue(steeringValue);

    // 触发输出流程
    this.triggerFlow('flow');
  }
}

/**
 * 创建流体模拟节点 (163)
 * 创建流体物理模拟
 */
export class CreateFluidSimulationNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加流体配置输入
    this.addInput({
      name: 'particleCount',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '粒子数量',
      defaultValue: 1000
    });

    this.addInput({
      name: 'viscosity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '粘度',
      defaultValue: 0.1
    });

    this.addInput({
      name: 'density',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '密度',
      defaultValue: 1000
    });

    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '初始位置',
      defaultValue: new Vector3(0, 0, 0)
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加流体系统输出
    this.addOutput({
      name: 'fluidSystem',
      type: SocketType.DATA,
      dataType: 'Object',
      direction: SocketDirection.OUTPUT,
      description: '流体系统'
    });
  }

  /**
   * 执行节点
   */
  public execute(): void {
    // 获取输入值
    const particleCount = this.getInputValue('particleCount') as number;
    const viscosity = this.getInputValue('viscosity') as number;
    const density = this.getInputValue('density') as number;
    const position = this.getInputValue('position') as Vector3;

    // 创建简化的流体系统配置
    const fluidConfig = {
      particleCount,
      viscosity,
      density,
      position,
      type: 'fluid',
      created: Date.now()
    };

    console.log('创建流体模拟:', fluidConfig);

    // 设置输出值
    this.setOutputValue('fluidSystem', fluidConfig);

    // 触发输出流程
    this.triggerFlow('flow');
  }
}

/**
 * 创建布料模拟节点 (164)
 * 创建布料物理模拟
 */
export class CreateClothSimulationNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加布料配置输入
    this.addInput({
      name: 'width',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '宽度',
      defaultValue: 10
    });

    this.addInput({
      name: 'height',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '高度',
      defaultValue: 10
    });

    this.addInput({
      name: 'stiffness',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '刚度',
      defaultValue: 100
    });

    this.addInput({
      name: 'damping',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '阻尼',
      defaultValue: 0.1
    });

    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '初始位置',
      defaultValue: new Vector3(0, 5, 0)
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加布料系统输出
    this.addOutput({
      name: 'clothSystem',
      type: SocketType.DATA,
      dataType: 'Object',
      direction: SocketDirection.OUTPUT,
      description: '布料系统'
    });
  }

  /**
   * 执行节点
   */
  public execute(): void {
    // 获取输入值
    const width = this.getInputValue('width') as number;
    const height = this.getInputValue('height') as number;
    const stiffness = this.getInputValue('stiffness') as number;
    const damping = this.getInputValue('damping') as number;
    const position = this.getInputValue('position') as Vector3;

    // 创建简化的布料系统配置
    const clothConfig = {
      width,
      height,
      stiffness,
      damping,
      position,
      type: 'cloth',
      created: Date.now()
    };

    console.log('创建布料模拟:', clothConfig);

    // 设置输出值
    this.setOutputValue('clothSystem', clothConfig);

    // 触发输出流程
    this.triggerFlow('flow');
  }
}

/**
 * 创建可破坏物体节点 (165)
 * 创建可破坏的物理对象
 */
export class CreateDestructibleNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    // 添加破坏配置输入
    this.addInput({
      name: 'destructionThreshold',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '破坏阈值',
      defaultValue: 100
    });

    this.addInput({
      name: 'fragmentCount',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '碎片数量',
      defaultValue: 10
    });

    this.addInput({
      name: 'fragmentSize',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '碎片大小',
      defaultValue: 0.5
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加可破坏物体输出
    this.addOutput({
      name: 'destructibleBody',
      type: SocketType.DATA,
      dataType: 'Object',
      direction: SocketDirection.OUTPUT,
      description: '可破坏物体'
    });
  }

  /**
   * 执行节点
   */
  public execute(): void {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const destructionThreshold = this.getInputValue('destructionThreshold') as number;
    const fragmentCount = this.getInputValue('fragmentCount') as number;
    const fragmentSize = this.getInputValue('fragmentSize') as number;

    if (!entity) {
      console.warn('CreateDestructibleNode: 实体无效');
      this.triggerFlow('flow');
      return;
    }

    // 创建简化的可破坏物体配置
    const destructibleConfig = {
      entity,
      destructionThreshold,
      fragmentCount,
      fragmentSize,
      type: 'destructible',
      created: Date.now(),
      isDestroyed: false
    };

    console.log('创建可破坏物体:', destructibleConfig);

    // 设置输出值
    this.setOutputValue('destructibleBody', destructibleConfig);

    // 触发输出流程
    this.triggerFlow('flow');
  }
}

/**
 * 注册物理节点
 * @param registry 节点注册表
 */
export function registerPhysicsNodes(registry: NodeRegistry): void {
  // 注册碰撞结束事件节点 (151)
  registry.registerNodeType({
    type: 'physics/collision/onCollisionExit',
    category: NodeCategory.EVENT,
    constructor: OnCollisionExitNode,
    label: '碰撞结束事件',
    description: '检测碰撞结束',
    icon: 'collision-exit',
    color: '#FF9800',
    tags: ['physics', 'collision', 'event', 'exit']
  });

  // 注册触发器进入事件节点 (152)
  registry.registerNodeType({
    type: 'physics/collision/onTriggerEnter',
    category: NodeCategory.EVENT,
    constructor: OnTriggerEnterNode,
    label: '触发器进入事件',
    description: '检测触发器进入',
    icon: 'trigger-enter',
    color: '#FF9800',
    tags: ['physics', 'trigger', 'event', 'enter']
  });

  // 注册触发器退出事件节点 (153)
  registry.registerNodeType({
    type: 'physics/collision/onTriggerExit',
    category: NodeCategory.EVENT,
    constructor: OnTriggerExitNode,
    label: '触发器退出事件',
    description: '检测触发器退出',
    icon: 'trigger-exit',
    color: '#FF9800',
    tags: ['physics', 'trigger', 'event', 'exit']
  });

  // 注册设置重力节点 (154)
  registry.registerNodeType({
    type: 'physics/world/setGravity',
    category: NodeCategory.PHYSICS,
    constructor: SetGravityNode,
    label: '设置重力',
    description: '设置物理世界重力',
    icon: 'gravity',
    color: '#9C27B0',
    tags: ['physics', 'world', 'gravity']
  });

  // 注册设置时间步长节点 (155)
  registry.registerNodeType({
    type: 'physics/world/setTimeStep',
    category: NodeCategory.PHYSICS,
    constructor: SetTimeStepNode,
    label: '设置时间步长',
    description: '设置物理模拟精度',
    icon: 'time-step',
    color: '#9C27B0',
    tags: ['physics', 'world', 'timestep']
  });

  // 注册创建角色控制器节点 (156)
  registry.registerNodeType({
    type: 'physics/character/createCharacterController',
    category: NodeCategory.PHYSICS,
    constructor: CreateCharacterControllerNode,
    label: '创建角色控制器',
    description: '创建角色物理控制器',
    icon: 'character-controller',
    color: '#3F51B5',
    tags: ['physics', 'character', 'controller']
  });

  // 注册移动角色节点 (157)
  registry.registerNodeType({
    type: 'physics/character/moveCharacter',
    category: NodeCategory.PHYSICS,
    constructor: MoveCharacterNode,
    label: '移动角色',
    description: '控制角色移动',
    icon: 'character-move',
    color: '#3F51B5',
    tags: ['physics', 'character', 'movement']
  });

  // 注册角色跳跃节点 (158)
  registry.registerNodeType({
    type: 'physics/character/jumpCharacter',
    category: NodeCategory.PHYSICS,
    constructor: JumpCharacterNode,
    label: '角色跳跃',
    description: '控制角色跳跃',
    icon: 'character-jump',
    color: '#3F51B5',
    tags: ['physics', 'character', 'jump']
  });

  // 注册创建载具节点 (159)
  registry.registerNodeType({
    type: 'physics/vehicle/createVehicle',
    category: NodeCategory.PHYSICS,
    constructor: CreateVehicleNode,
    label: '创建载具',
    description: '创建物理载具',
    icon: 'vehicle',
    color: '#FF5722',
    tags: ['physics', 'vehicle', 'create']
  });

  // 注册设置引擎力节点 (160)
  registry.registerNodeType({
    type: 'physics/vehicle/setEngineForce',
    category: NodeCategory.PHYSICS,
    constructor: SetEngineForceNode,
    label: '设置引擎力',
    description: '设置载具引擎推力',
    icon: 'engine',
    color: '#FF5722',
    tags: ['physics', 'vehicle', 'engine']
  });

  // 注册设置制动力节点 (161)
  registry.registerNodeType({
    type: 'physics/vehicle/setBrakeForce',
    category: NodeCategory.PHYSICS,
    constructor: SetBrakeForceNode,
    label: '设置制动力',
    description: '设置载具制动力',
    icon: 'brake',
    color: '#FF5722',
    tags: ['physics', 'vehicle', 'brake']
  });

  // 注册设置转向值节点 (162)
  registry.registerNodeType({
    type: 'physics/vehicle/setSteeringValue',
    category: NodeCategory.PHYSICS,
    constructor: SetSteeringValueNode,
    label: '设置转向值',
    description: '设置载具转向角度',
    icon: 'steering',
    color: '#FF5722',
    tags: ['physics', 'vehicle', 'steering']
  });

  // 注册创建流体模拟节点 (163)
  registry.registerNodeType({
    type: 'physics/fluid/createFluidSimulation',
    category: NodeCategory.PHYSICS,
    constructor: CreateFluidSimulationNode,
    label: '创建流体模拟',
    description: '创建流体物理模拟',
    icon: 'fluid',
    color: '#00BCD4',
    tags: ['physics', 'fluid', 'simulation']
  });

  // 注册创建布料模拟节点 (164)
  registry.registerNodeType({
    type: 'physics/cloth/createClothSimulation',
    category: NodeCategory.PHYSICS,
    constructor: CreateClothSimulationNode,
    label: '创建布料模拟',
    description: '创建布料物理模拟',
    icon: 'cloth',
    color: '#00BCD4',
    tags: ['physics', 'cloth', 'simulation']
  });

  // 注册创建可破坏物体节点 (165)
  registry.registerNodeType({
    type: 'physics/destruction/createDestructible',
    category: NodeCategory.PHYSICS,
    constructor: CreateDestructibleNode,
    label: '创建可破坏物体',
    description: '创建可破坏的物理对象',
    icon: 'destruction',
    color: '#00BCD4',
    tags: ['physics', 'destruction', 'breakable']
  });

  // 注册射线检测节点
  registry.registerNodeType({
    type: 'physics/raycast',
    category: NodeCategory.PHYSICS,
    constructor: RaycastNode,
    label: '射线检测',
    description: '执行物理射线检测',
    icon: 'ray',
    color: '#E91E63',
    tags: ['physics', 'raycast', 'collision']
  });

  // 注册应用力节点
  registry.registerNodeType({
    type: 'physics/applyForce',
    category: NodeCategory.PHYSICS,
    constructor: ApplyForceNode,
    label: '应用力',
    description: '向物理体应用力',
    icon: 'force',
    color: '#E91E63',
    tags: ['physics', 'force', 'dynamics']
  });

  // 注册碰撞检测节点
  registry.registerNodeType({
    type: 'physics/collisionDetection',
    category: NodeCategory.PHYSICS,
    constructor: CollisionDetectionNode,
    label: '碰撞检测',
    description: '检测两个实体之间的碰撞',
    icon: 'collision',
    color: '#E91E63',
    tags: ['physics', 'collision', 'detection']
  });

  // 注册物理约束节点
  registry.registerNodeType({
    type: 'physics/createConstraint',
    category: NodeCategory.PHYSICS,
    constructor: CreateConstraintNode,
    label: '创建约束',
    description: '创建物理约束',
    icon: 'constraint',
    color: '#E91E63',
    tags: ['physics', 'constraint', 'joint']
  });

  // 注册物理材质节点
  registry.registerNodeType({
    type: 'physics/createMaterial',
    category: NodeCategory.PHYSICS,
    constructor: CreatePhysicsMaterialNode,
    label: '创建物理材质',
    description: '创建物理材质',
    icon: 'material',
    color: '#E91E63',
    tags: ['physics', 'material']
  });
}
